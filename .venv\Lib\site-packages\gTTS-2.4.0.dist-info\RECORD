../../Scripts/gtts-cli.exe,sha256=016roKIXJqCeMev4GXHlEBjx_D5QIGG1VmW8DEnyukU,108373
gTTS-2.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gTTS-2.4.0.dist-info/LICENSE,sha256=CdnrHRAGm0eW6Dyx3zPvl9ROx7wLq85oOR0ed_5M30U,1093
gTTS-2.4.0.dist-info/METADATA,sha256=S6qYH4AqrxzsNHX9Oy8bFoXmxKcr0QNC6iroeJTLNhQ,4136
gTTS-2.4.0.dist-info/RECORD,,
gTTS-2.4.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gTTS-2.4.0.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
gTTS-2.4.0.dist-info/entry_points.txt,sha256=bI9BIrpE9bB1FVhrm3FHJrfQTsIcnfngqvGqUMNLthM,46
gTTS-2.4.0.dist-info/top_level.txt,sha256=OFgvuVh7ic8wSxEPlCYvHKE8FsIkAr2fdzGCppOFcZI,5
gtts/__init__.py,sha256=-eS_P97t5UURXHjLkZ3VXUp-Hixo6AR9TXvfxpTo8js,137
gtts/__pycache__/__init__.cpython-310.pyc,,
gtts/__pycache__/accents.cpython-310.pyc,,
gtts/__pycache__/cli.cpython-310.pyc,,
gtts/__pycache__/lang.cpython-310.pyc,,
gtts/__pycache__/langs.cpython-310.pyc,,
gtts/__pycache__/tts.cpython-310.pyc,,
gtts/__pycache__/utils.cpython-310.pyc,,
gtts/__pycache__/version.cpython-310.pyc,,
gtts/accents.py,sha256=V3LpPxaIyP1byKjJTH45lYanrKRU9Tb5A_z5XsaxFYE,2050
gtts/cli.py,sha256=3LAfzOyNIm0-avf5nf7xXdyfLmz9DTOoBS1Y-lfrZ4M,5697
gtts/lang.py,sha256=gRORF4N-U0DmDaRd7r26wDDERfm1gF4sSnAqaGAZsIQ,2899
gtts/langs.py,sha256=qtvYnzUhgi20znfkk227riK7PSFiHUBAdxeUeY08aJM,1343
gtts/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gtts/tests/__pycache__/__init__.cpython-310.pyc,,
gtts/tests/__pycache__/test_cli.cpython-310.pyc,,
gtts/tests/__pycache__/test_lang.cpython-310.pyc,,
gtts/tests/__pycache__/test_tts.cpython-310.pyc,,
gtts/tests/__pycache__/test_utils.cpython-310.pyc,,
gtts/tests/input_files/test_cli_test_ascii.txt,sha256=hKI7P_x4Bu7t3iUCxTD-q-LWu8pmFMXUHDDLbNSYDGE,188
gtts/tests/input_files/test_cli_test_utf8.txt,sha256=5I3Fwoy0m1Q_jJRLhjn-Pivyc9g88YVut2BmNT_aLms,232
gtts/tests/test_cli.py,sha256=NRqECpZqb2ba68Q_0Ioboh-0dwd3mrMnplj7zFJq7Vk,7065
gtts/tests/test_lang.py,sha256=VhJGVI8ibms6Gv_cCuz2D4oZcQieTIimUDKj7haixM0,591
gtts/tests/test_tts.py,sha256=LUXDAO3KWzUsCE-RxzFPqDnWtPO8Os-7SSph-yomV4o,5421
gtts/tests/test_utils.py,sha256=pFm_t1P0JG5i8XLkXi6X4K8goSVtD_GAd8HtrPiaTFk,1561
gtts/tokenizer/__init__.py,sha256=99kDIHYtzOr9BQgVvYP9fhszuM5YOlsumuLhTmeLvmI,136
gtts/tokenizer/__pycache__/__init__.cpython-310.pyc,,
gtts/tokenizer/__pycache__/core.cpython-310.pyc,,
gtts/tokenizer/__pycache__/pre_processors.cpython-310.pyc,,
gtts/tokenizer/__pycache__/symbols.cpython-310.pyc,,
gtts/tokenizer/__pycache__/tokenizer_cases.cpython-310.pyc,,
gtts/tokenizer/core.py,sha256=DYiciWvM_ZyRMOq2P0NGk-BHeC98IA_JyQi31aNJljc,10908
gtts/tokenizer/pre_processors.py,sha256=ZqefZAe5_SFzrsK2cd7Fj4iZXPYhS-eyXWVTLpf9n6o,1579
gtts/tokenizer/symbols.py,sha256=EkutlUL27oHgk0ntdr9nOZ0PS6-LpQAjhfGme9Ig9mQ,258
gtts/tokenizer/tests/__pycache__/test_core.cpython-310.pyc,,
gtts/tokenizer/tests/__pycache__/test_pre_processors.cpython-310.pyc,,
gtts/tokenizer/tests/__pycache__/test_tokenizer_cases.cpython-310.pyc,,
gtts/tokenizer/tests/test_core.py,sha256=yPQ6zSRvB704FXQJO7MIApXT2JMO3PS66VTUcQHxgeM,2162
gtts/tokenizer/tests/test_pre_processors.py,sha256=8tl2OSLsOS2RGXcPWOFbi9J2ExD38Qb8eZU7PnH6F7Y,798
gtts/tokenizer/tests/test_tokenizer_cases.py,sha256=E6O2g7wSKz6AoHO27CJ4OS7Y7XwWG99c7p2xwaQGZYo,1588
gtts/tokenizer/tokenizer_cases.py,sha256=uYtQnj4UDBC_Ow3kjnpm1O0_QvknA1w1LNjBzmd8XfQ,1955
gtts/tts.py,sha256=brCo0NN3s2f4qHm1LuTK_Eem5kQmR7t_Hua5EFVpOHg,12708
gtts/utils.py,sha256=WMB97ZCDDfeS5FfN25LRWDKmwITUJgiinPHjreBThig,3417
gtts/version.py,sha256=yLaFvd-K80rs_ClRVYULStijkok4RfYSaanIt_E-aKM,22
