# AI Card Visit Application - API Configuration
# Cấu hình API cho ứng dụng AI Card Visit

# === PRIMARY API - GEMINI (REQUIRED) ===
# Google Gemini API - Dùng cho OCR và AI Image Generation
# Get key: https://ai.google.dev/
# Cost: FREE tier available
GEMINI_API_KEY=AIzaSyAPQderfh9QbvR-uxpyhqLfdXA53-FyXu0

# Backup Gemini API Keys (để tránh rate limit)
GEMINI_API_KEY_2=AIzaSyDeZ4KQhbqAbyiPVVoo1jTTNENQEeF7RzU
GEMINI_API_KEY_3=AIzaSyDigOhAC3tfNkmDqLxrx7HmdtZAWwOR0QQ

# === BACKUP APIs (OPTIONAL) ===

# OpenAI DALL-E 3 (Backup for image generation)
# Get key: https://platform.openai.com/api-keys
# Cost: ~$0.04 per image
# OPENAI_API_KEY=********************************************************************************************************************************************************************

# Hugging Face API (Backup for image generation)
# Get token: https://huggingface.co/settings/tokens
# Cost: FREE
HUGGINGFACE_TOKEN=hf_demo_token_for_testing

# Replicate API (Alternative image generation)
# Get token: https://replicate.com/account/api-tokens
# Cost: ~$0.003-0.01 per image
REPLICATE_API_TOKEN=your_replicate_token_here

# === APPLICATION SETTINGS ===
# Flask application settings
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_PORT=5000
FLASK_HOST=0.0.0.0
