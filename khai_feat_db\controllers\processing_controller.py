"""
Processing Controller - Xử lý xử lý ảnh và tạo AI
Handle image processing and AI generation
"""

from flask import Blueprint, jsonify, request, redirect, url_for, render_template
import json
import traceback
from pathlib import Path
from datetime import datetime

from gemini_ocr_service import GeminiOCRService
from ai_generator import AIImageGenerator
from models.session_model import SessionModel
from ai_config import get_gemini_config


class ProcessingController:
    """Controller xử lý ảnh và tạo AI - Controller for image processing and AI generation"""

    def __init__(self):
        """Khởi tạo Processing Controller"""
        self.blueprint = Blueprint('processing', __name__)
        self.setup_routes()
        self.setup_models()

    def setup_routes(self):
        """<PERSON>hi<PERSON><PERSON> lập các routes xử lý - Setup processing routes"""
        # Route xử lý ảnh chính
        self.blueprint.add_url_rule('/process_images', 'process_images', self.process_images, methods=['POST'])
        # Route retry AI generation
        self.blueprint.add_url_rule('/retry_ai_generation', 'retry_ai_generation', self.retry_ai_generation, methods=['POST'])

    def setup_models(self):
        """Khởi tạo các model xử lý và AI pipeline - Initialize processing models and AI pipeline"""
        self.ocr_service = GeminiOCRService()
        self.ai_generator = AIImageGenerator()
        self.session_model = SessionModel()

        # Khởi tạo two-stage AI pipeline
        try:
            from services.ai_pipeline_service import AIProcessingPipeline
            self.ai_pipeline = AIProcessingPipeline()
            print("✅ Two-Stage AI Pipeline initialized in Processing Controller")
        except Exception as e:
            print(f"⚠️ AI Pipeline initialization failed: {e}")
            self.ai_pipeline = None

    def process_images(self):
        """Xử lý ảnh đã chụp và tạo ảnh AI - Process captured images and generate AI image"""
        try:
            # Import camera controller để lấy session chung
            from controllers.camera_controller import camera_controller

            # Sử dụng session từ camera controller (instance chung)
            if not camera_controller.session_model.current_session:
                return jsonify({
                    'status': 'error',
                    'message': 'No active session found. Please capture images first.'
                }), 400

            session = camera_controller.session_model.current_session
            print(f"📁 Using session: {session.get('session_id')}")
            print(f"📁 Session status: card={session.get('card_captured')}, face={session.get('face_captured')}")

            # Kiểm tra đã chụp đủ ảnh chưa
            if not session.get('card_captured') or not session.get('face_captured'):
                return jsonify({
                    'status': 'error',
                    'message': 'Please capture both business card and face images first.'
                }), 400

            # Lấy template prompt từ request
            prompt_template = 'cartoon'  # Mặc định
            try:
                if request.is_json and request.get_json():
                    data = request.get_json()
                    prompt_template = data.get('prompt_template', 'cartoon')
                    print(f"🎨 Selected prompt template: {prompt_template}")
                else:
                    print("🎨 Using default prompt template (no JSON data received)")
            except Exception as e:
                print(f"⚠️ Error parsing request data: {e}, using default prompt")

            # Sử dụng cấu hình AI với prompt đã chọn
            ai_config = get_gemini_config()
            ai_config['prompt_template'] = prompt_template
            print(f"🤖 Using AI config: Model={ai_config['model']}, Template={prompt_template}")

            # Cập nhật trạng thái session
            camera_controller.session_model.update_session(status='processing', ai_config=ai_config)

            # Lấy đường dẫn ảnh từ session thực tế
            card_image_path = session.get('card_image')
            face_image_path = session.get('face_image')

            print(f"🔍 Image paths from session:")
            print(f"   📄 Card image: {card_image_path}")
            print(f"   👤 Face image: {face_image_path}")

            # Xác thực đầu vào
            if not card_image_path or not Path(card_image_path).exists():
                return jsonify({
                    'status': 'error',
                    'message': 'Business card image not found. Please capture business card first.',
                    'debug': {
                        'card_image_path': card_image_path,
                        'card_image_exists': Path(card_image_path).exists() if card_image_path else False
                    }
                }), 500

            if not face_image_path or not Path(face_image_path).exists():
                return jsonify({
                    'status': 'error',
                    'message': 'Face image not found. Please capture face image first.',
                    'debug': {
                        'face_image_path': face_image_path,
                        'face_image_exists': Path(face_image_path).exists() if face_image_path else False
                    }
                }), 500

            # Sử dụng Three-Stage AI Pipeline với xử lý song song
            print("� Starting Three-Stage AI Pipeline with parallel processing...")
            try:
                if hasattr(self, 'ai_pipeline') and self.ai_pipeline:
                    # Sử dụng pipeline mới với xử lý song song Stage 1.5 + Stage 2
                    pipeline_result = self.ai_pipeline.process_business_card(
                        card_image_path=card_image_path,
                        face_image_path=face_image_path,
                        prompt_template=prompt_template,
                        session_id=session.get('session_id')
                    )

                    if pipeline_result and pipeline_result.get('success'):
                        print("✅ Three-Stage Pipeline completed successfully!")

                        # Lấy kết quả từ pipeline
                        ocr_result = pipeline_result['stage1_ocr']['data']
                        tts_result = pipeline_result.get('stage1_5_tts', {})
                        generation_result = pipeline_result['stage2_generation']

                        # Save OCR data to session
                        camera_controller.session_model.save_card_info(ocr_result)
                        print("💾 OCR data saved to session")

                        # Save generated images to session - Debug structure
                        print(f"DEBUG Generation result structure: {list(generation_result.keys())}")
                        if generation_result.get('success'):
                            generation_data = generation_result.get('data', {})
                            print(f"DEBUG Generation data keys: {list(generation_data.keys()) if generation_data else 'None'}")
                            generated_images = generation_data.get('generated_images', [])
                            print(f"DEBUG Found {len(generated_images)} generated images")

                            if generated_images:
                                camera_controller.session_model.save_generated_images(generated_images)
                                print(f"�️ Generated {len(generated_images)} images")
                            else:
                                print("WARNING No generated images found in pipeline result")
                                print(f"DEBUG Full generation_result keys: {list(generation_result.keys())}")
                                print(f"DEBUG Full generation_data: {generation_data}")
                        else:
                            print(f"ERROR Generation failed: {generation_result.get('error', 'Unknown error')}")

                        # Log TTS result và save audio path
                        print(f"DEBUG TTS result: {tts_result}")
                        if tts_result.get('success'):
                            print(f"🔊 TTS completed: {tts_result.get('status', 'unknown')}")
                            if tts_result.get('audio_path'):
                                print(f"🎵 Audio file: {tts_result['audio_path']}")
                                # Save audio path to session
                                camera_controller.session_model.current_session['tts_audio_path'] = tts_result['audio_path']
                                print(f"DEBUG Audio path saved to session: {tts_result['audio_path']}")
                            else:
                                print("DEBUG No audio_path in tts_result")
                        else:
                            print("DEBUG TTS not successful")

                    else:
                        print("❌ Pipeline failed - implementing partial success")

                        # Kiểm tra xem có OCR data không để implement partial success
                        ocr_result = pipeline_result.get('stage1_ocr', {}).get('data', {})

                        if ocr_result and any(v for v in ocr_result.values() if v and str(v).strip()):
                            # Có OCR data - trả về partial success
                            print("✅ OCR data available - returning partial success")
                            return jsonify({
                                'status': 'partial_success',  # Không phải 'error'!
                                'message': 'OCR completed successfully. AI generation failed but can be retried.',
                                'card_info': ocr_result,  # OCR data vẫn được trả về
                                'generated_images': [],
                                'processing_steps': {
                                    'ocr': 'success',
                                    'ai_generation': 'failed'
                                },
                                'can_retry_ai': True  # Cho phép retry
                            })
                        else:
                            # Không có OCR data - trả về error
                            return jsonify({
                                'status': 'error',
                                'message': f"Pipeline processing failed: {pipeline_result.get('error', 'Unknown error')}",
                                'step': 'pipeline'
                            }), 500

                else:
                    print("❌ Pipeline not available")
                    return jsonify({
                        'status': 'error',
                        'message': 'Three-Stage Pipeline not available',
                        'step': 'pipeline'
                    }), 500

            except Exception as e:
                print(f"❌ Pipeline Error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': f'Pipeline processing error: {str(e)}',
                    'step': 'pipeline'
                }), 500

            # Update session status và return success với đầy đủ data cho frontend
            camera_controller.session_model.update_session(status='completed')
            print("✅ Session completed successfully (Pipeline)")

            # Lấy data để return cho frontend
            final_generated_images = []
            if generation_result.get('success'):
                generation_data = generation_result.get('data', {})
                final_generated_images = generation_data.get('generated_images', [])

            return jsonify({
                'status': 'success',
                'message': 'Three-Stage Pipeline completed successfully with TTS',
                'card_info': ocr_result,
                'generated_images': final_generated_images,
                'session_id': session.get('session_id'),
                'processing_details': {
                    'pipeline': 'success',
                    'stages_completed': ['OCR', 'TTS', 'AI Generation'],
                    'tts_status': tts_result.get('status', 'unknown') if tts_result.get('success') else 'failed',
                    'images_count': len(final_generated_images)
                }
            })

        except Exception as e:
            print(f"❌ Processing error: {e}")
            traceback.print_exc()
            return jsonify({
                'status': 'error',
                'message': f'Processing failed: {str(e)}'
            }), 500

    def retry_ai_generation(self):
        """Retry AI generation for existing session with OCR data"""
        try:
            # Import camera controller để lấy session chung
            from controllers.camera_controller import camera_controller

            # Check if session exists
            if not camera_controller.session_model.current_session:
                return jsonify({
                    'status': 'error',
                    'message': 'No active session found. Please capture images first.'
                }), 400

            session = camera_controller.session_model.current_session
            print(f"🔄 Retrying AI generation for session: {session.get('session_id')}")

            # Check if OCR data exists
            if not session.get('card_info'):
                return jsonify({
                    'status': 'error',
                    'message': 'No OCR data found. Please run OCR processing first.'
                }), 400

            # Get face image path
            face_image_path = session.get('face_image')
            if not face_image_path or not Path(face_image_path).exists():
                return jsonify({
                    'status': 'error',
                    'message': 'Face image not found. Please capture face image first.'
                }), 400

            # Get prompt template from request
            prompt_template = 'cartoon'  # Default
            try:
                if request.is_json and request.get_json():
                    data = request.get_json()
                    prompt_template = data.get('prompt_template', 'cartoon')
                    print(f"🎨 Using prompt template: {prompt_template}")
            except Exception as e:
                print(f"⚠️ Error parsing request data: {e}, using default prompt")

            # Get OCR data from session
            card_info = session.get('card_info')
            print(f"📄 Using existing OCR data: {card_info.get('name', 'N/A')} - {card_info.get('company', 'N/A')}")

            # Try AI generation
            print("🎨 Starting AI Image Generation retry...")
            try:
                if self.ai_generator:
                    generation_result = self.ai_generator.generate_ai_image(
                        face_image_path=face_image_path,
                        card_info=card_info,
                        prompt_template=prompt_template,
                        session_id=session.get('session_id')
                    )

                    if generation_result and generation_result.get('success'):
                        print("✅ AI Image Generation retry successful!")
                        print(f"🖼️ Generated {len(generation_result.get('generated_images', []))} images")

                        # Update session to completed
                        camera_controller.session_model.update_session(status='completed')

                        return jsonify({
                            'status': 'success',
                            'message': 'AI generation completed successfully',
                            'card_info': card_info,
                            'generated_images': generation_result.get('generated_images', []),
                            'session_id': session.get('session_id'),
                            'retry': True
                        })
                    else:
                        generation_error = generation_result.get('error', 'AI generation failed') if generation_result else 'AI generator returned no result'
                        print(f"❌ AI Generation retry failed: {generation_error}")

                        return jsonify({
                            'status': 'error',
                            'message': f'AI generation retry failed: {generation_error}',
                            'card_info': card_info,
                            'generated_images': [],
                            'session_id': session.get('session_id'),
                            'retry': True,
                            'error_details': generation_error
                        }), 500
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'AI generator not available',
                        'card_info': card_info,
                        'retry': True
                    }), 500

            except Exception as e:
                print(f"❌ AI Generation retry error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': f'AI generation retry failed: {str(e)}',
                    'card_info': card_info,
                    'retry': True
                }), 500

        except Exception as e:
            print(f"❌ Retry AI generation error: {e}")
            traceback.print_exc()
            return jsonify({
                'status': 'error',
                'message': f'Retry failed: {str(e)}'
            }), 500


# Create global processing controller instance
processing_controller = ProcessingController()

def get_processing_blueprint():
    """Get processing controller blueprint"""
    return processing_controller.blueprint
